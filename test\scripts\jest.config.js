module.exports = {
  displayName: 'Scripts Tests',
  testEnvironment: 'node',
  rootDir: '../../',
  testMatch: ['<rootDir>/test/scripts/**/*.spec.ts'],
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '<rootDir>/scripts/**/*.(t|j)s',
    '!<rootDir>/scripts/**/*.d.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/scripts',
  moduleFileExtensions: ['js', 'json', 'ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^scripts/(.*)$': '<rootDir>/scripts/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/test/config/setup.ts'],
  testTimeout: 30000,
  verbose: true,
};
