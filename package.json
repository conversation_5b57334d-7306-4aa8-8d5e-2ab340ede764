{"name": "api", "version": "0.4.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "prod:build": "npx prisma generate && npx prisma migrate deploy && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:payment": "jest --config ./test/payment/jest.config.js", "test:payment:watch": "jest --config ./test/payment/jest.config.js --watch", "test:payment:cov": "jest --config ./test/payment/jest.config.js --coverage", "test:payment:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config ./test/payment/jest.config.js --runInBand", "test:payment:service": "jest --config ./test/payment/jest.config.js test/payment/unified-payment.service.spec.ts", "test:payment:controller": "jest --config ./test/payment/jest.config.js test/payment/unified-payment.controller.spec.ts", "test:payment:email": "jest --config ./test/payment/jest.config.js test/payment/email-template.spec.ts", "test:payment:integration": "jest --config ./test/payment/jest.config.js test/payment/payment.integration.spec.ts", "test:document": "jest --config ./test/document/jest.config.js", "test:document:basic": "jest --config ./test/document/jest.config.js test/document/document-basic.spec.ts", "test:document:vault": "jest --config ./test/document/jest.config.js test/document/document-vault.spec.ts", "test:security": "jest --config ./test/security/jest.config.js", "test:security:watch": "jest --config ./test/security/jest.config.js --watch", "test:security:cov": "jest --config ./test/security/jest.config.js --coverage", "test:security:service": "jest --config ./test/security/jest.config.js test/security/security.service.spec.ts", "test:security:gdpr": "jest --config ./test/security/jest.config.js test/security/gdpr.service.spec.ts", "test:security:guard": "jest --config ./test/security/jest.config.js test/security/application-access.guard.spec.ts", "test:workflow": "jest --config ./test/workflow/jest.config.js", "test:workflow:watch": "jest --config ./test/workflow/jest.config.js --watch", "test:workflow:cov": "jest --config ./test/workflow/jest.config.js --coverage", "test:workflow:service": "jest --config ./test/workflow/jest.config.js test/workflow/workflow-master.service.spec.ts", "test:workflow:controller": "jest --config ./test/workflow/jest.config.js test/workflow/workflow-master.controller.spec.ts", "test:workflow:integration": "jest --config ./test/workflow/jest.config.js test/workflow/workflow-master.integration.spec.ts", "test:workflow-template": "jest --config ./test/workflow-template/jest.config.js", "test:workflow-template:watch": "jest --config ./test/workflow-template/jest.config.js --watch", "test:workflow-template:cov": "jest --config ./test/workflow-template/jest.config.js --coverage", "test:workflow-template:service": "jest --config ./test/workflow-template/jest.config.js test/workflow-template/workflow-template.service.spec.ts", "test:workflow-template:controller": "jest --config ./test/workflow-template/jest.config.js test/workflow-template/workflow-template.controller.spec.ts", "test:workflow-template:integration": "jest --config ./test/workflow-template/jest.config.js test/workflow-template/workflow-template.integration.spec.ts", "test:validation": "jest --config ./test/validation/jest.config.js", "test:validation:watch": "jest --config ./test/validation/jest.config.js --watch", "test:validation:cov": "jest --config ./test/validation/jest.config.js --coverage", "test:agent": "jest --config ./test/agent/jest.config.js", "test:agent:watch": "jest --config ./test/agent/jest.config.js --watch", "test:agent:cov": "jest --config ./test/agent/jest.config.js --coverage", "test:agent:service": "jest --config ./test/agent/jest.config.js test/agent/agent.service.spec.ts", "test:agent:controller": "jest --config ./test/agent/jest.config.js test/agent/agent.controller.spec.ts", "test:agent:integration": "jest --config ./test/agent/jest.config.js test/agent/agent.integration.spec.ts", "test:agent:application": "jest --config ./test/agent/jest.config.js test/agent/application-management.spec.ts", "test:user": "jest --config ./test/user/jest.config.js", "test:user:watch": "jest --config ./test/user/jest.config.js --watch", "test:user:cov": "jest --config ./test/user/jest.config.js --coverage", "test:user:service": "jest --config ./test/user/jest.config.js test/user/user.service.spec.ts", "test:user:controller": "jest --config ./test/user/jest.config.js test/user/user.controller.spec.ts", "test:user:integration": "jest --config ./test/user/jest.config.js test/user/user.integration.spec.ts", "test:user:dto": "jest --config ./test/user/jest.config.js test/user/user-dto.validation.spec.ts", "test:immigration": "jest --config ./test/immigration/jest.config.js", "test:immigration:watch": "jest --config ./test/immigration/jest.config.js --watch", "test:immigration:cov": "jest --config ./test/immigration/jest.config.js --coverage", "test:immigration:service": "jest --config ./test/immigration/jest.config.js test/immigration/immigration.service.spec.ts", "test:immigration:controller": "jest --config ./test/immigration/jest.config.js test/immigration/immigration.controller.spec.ts", "test:immigration:integration": "jest --config ./test/immigration/jest.config.js test/immigration/immigration.integration.spec.ts", "test:template": "jest --config ./test/template/jest.config.js", "test:template:watch": "jest --config ./test/template/jest.config.js --watch", "test:template:cov": "jest --config ./test/template/jest.config.js --coverage", "test:email-integration": "jest --config ./test/application/jest.config.js test/application/email-template-integration.spec.ts", "test:email-integration:watch": "jest --config ./test/application/jest.config.js test/application/email-template-integration.spec.ts --watch", "test:email-integration:cov": "jest --config ./test/application/jest.config.js test/application/email-template-integration.spec.ts --coverage", "migrate:dev": "prisma migrate dev", "db:push": "prisma db push", "migrate:reset": "prisma migrate reset", "db:seed": "prisma db seed", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "script:missing-document-reminder": "ts-node scripts/missing-document-reminder.ts", "script:missing-document-reminder:scheduler": "ts-node scripts/missing-document-reminder.ts --scheduler", "script:missing-document-reminder:status": "ts-node scripts/missing-document-reminder.ts --status", "scheduler:start": "ts-node scripts/scheduler-manager.ts start", "scheduler:stop": "ts-node scripts/scheduler-manager.ts stop", "scheduler:restart": "ts-node scripts/scheduler-manager.ts restart", "scheduler:status": "ts-node scripts/scheduler-manager.ts status", "test:missing-document-reminder": "jest --config ./test/scripts/jest.config.js", "test:missing-document-reminder:watch": "jest --config ./test/scripts/jest.config.js --watch", "test:missing-document-reminder:cov": "jest --config ./test/scripts/jest.config.js --coverage"}, "dependencies": {"@fastify/multipart": "^8.2.0", "@fastify/static": "^7.0.2", "@nest-lab/fastify-multer": "^1.2.0", "@nestjs/class-transformer": "^0.4.0", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.3.7", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.3.1", "@prisma/client": "^6.5.0", "@react-email/components": "^0.0.32", "@supabase/supabase-js": "^2.43.1", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "mammoth": "^1.7.2", "nodemailer": "^6.9.13", "openai": "^4.47.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfmake": "^0.2.10", "pdfreader": "^3.0.2", "reflect-metadata": "^0.2.0", "resend": "^4.1.1", "rxjs": "^7.8.1", "stripe": "^17.4.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/react": "^19.0.7", "@types/supertest": "^6.0.0", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "axios": "^1.10.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "resolutions": {"string-width": "4.2.3"}}