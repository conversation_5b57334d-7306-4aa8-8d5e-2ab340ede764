/**
 * PM2 Ecosystem Configuration
 *
 * Simple production process management configuration for Career Ireland API services.
 * Includes the main API service and the missing document reminder scheduler.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-18
 */

module.exports = {
  apps: [
    {
      // Main API Service
      name: 'api',
      script: 'dist/src/main.js',
      cwd: '/root/api',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 4242,
      },
    },

    {
      // Missing Document Reminder Scheduler Service
      name: 'scheduler',
      script: 'npm',
      args: 'run scheduler:start',
      cwd: '/root/api',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production',
        SCHEDULER_ENABLED: 'true',
        SCHEDULER_HOUR: '9',
        SCHEDULER_MINUTE: '0',
        SCHEDULER_TIMEZONE: 'UTC',
        SCHEDULER_CHECK_INTERVAL: '60000',
        SCHEDULER_LOG_LEVEL: 'warn',
      },
    },
  ],
};
