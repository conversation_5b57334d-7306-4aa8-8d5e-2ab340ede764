/**
 * PM2 Ecosystem Configuration
 * 
 * Production process management configuration for Career Ireland API services.
 * Includes the main API service and the missing document reminder scheduler.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-18
 */

module.exports = {
  apps: [
    {
      // Main API Service (Admin Service)
      name: 'admin',
      script: 'dist/main.js',
      cwd: '/root/api',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 4242,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4242,
      },
      // Logging configuration
      log_file: '/root/api/logs/admin-combined.log',
      out_file: '/root/api/logs/admin-out.log',
      error_file: '/root/api/logs/admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Health monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Advanced options
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      
      // Environment-specific settings
      node_args: '--max-old-space-size=1024',
    },
    
    {
      // Missing Document Reminder Scheduler Service
      name: 'scheduler',
      script: 'scripts/missing-document-reminder.ts',
      cwd: '/root/api',
      interpreter: 'npx',
      interpreter_args: 'ts-node',
      args: '--scheduler',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        SCHEDULER_ENABLED: 'true',
        SCHEDULER_HOUR: '9',
        SCHEDULER_MINUTE: '0',
        SCHEDULER_TIMEZONE: 'UTC',
        SCHEDULER_CHECK_INTERVAL: '60000',
        SCHEDULER_LOG_LEVEL: 'info',
      },
      env_production: {
        NODE_ENV: 'production',
        SCHEDULER_ENABLED: 'true',
        SCHEDULER_HOUR: '9',
        SCHEDULER_MINUTE: '0',
        SCHEDULER_TIMEZONE: 'UTC',
        SCHEDULER_CHECK_INTERVAL: '60000',
        SCHEDULER_LOG_LEVEL: 'warn',
      },
      
      // Logging configuration
      log_file: '/root/api/logs/scheduler-combined.log',
      out_file: '/root/api/logs/scheduler-out.log',
      error_file: '/root/api/logs/scheduler-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 5000,
      max_restarts: 5,
      min_uptime: '30s',
      
      // Health monitoring
      health_check_grace_period: 5000,
      health_check_fatal_exceptions: true,
      
      // Advanced options
      kill_timeout: 10000,
      listen_timeout: 5000,
      shutdown_with_message: true,
      
      // Scheduler-specific settings
      node_args: '--max-old-space-size=512',
      
      // Cron restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
    }
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'root',
      host: ['your-production-server.com'],
      ref: 'origin/production',
      repo: '**************:your-org/careerireland-api.git',
      path: '/root/api',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run prod:build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    staging: {
      user: 'root',
      host: ['your-staging-server.com'],
      ref: 'origin/staging',
      repo: '**************:your-org/careerireland-api.git',
      path: '/root/api-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run prod:build && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
